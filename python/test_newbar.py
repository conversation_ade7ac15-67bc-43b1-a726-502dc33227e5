#!/usr/bin/env python3
"""
测试NewBar Python绑定功能
"""
from datetime import datetime, timedelta
from rs_czsc._rs_czsc import RawBar, NewBar, Freq, CZSC, Direction, Mark

def test_newbar_creation():
    """测试NewBar对象创建"""
    print("🔍 测试NewBar创建...")
    
    # 创建一些RawBar作为elements
    base_dt = datetime(2025, 1, 1)
    raw_bars = []
    
    for i in range(3):
        dt = base_dt + timedelta(hours=i)
        unix_timestamp = int(dt.timestamp())
        
        raw_bar = RawBar(
            symbol="TEST.SZ",
            dt_utc_timestamp=unix_timestamp,
            freq=Freq.F60,
            open=100.0 + i,
            close=101.0 + i,
            high=102.0 + i,
            low=99.0 + i,
            vol=1000000.0,
            amount=101000000.0 + i * 1000000
        )
        raw_bars.append(raw_bar)
    
    # 创建NewBar
    new_bar = NewBar(
        symbol="TEST.SZ",
        dt_utc_timestamp=int(base_dt.timestamp()),
        freq=Freq.F60,
        id=1,
        open=100.0,
        close=103.0,
        high=104.0,
        low=99.0,
        vol=3000000.0,
        amount=306000000.0,
        elements=raw_bars
    )
    
    print(f"✓ NewBar创建成功: {new_bar}")
    return new_bar

def test_newbar_properties(new_bar):
    """测试NewBar属性"""
    print("\n📊 测试NewBar属性...")
    
    print(f"  符号: {new_bar.symbol}")
    print(f"  时间: {new_bar.dt}")
    print(f"  频率: {new_bar.freq}")
    print(f"  ID: {new_bar.id}")
    print(f"  开盘价: {new_bar.open}")
    print(f"  收盘价: {new_bar.close}")
    print(f"  最高价: {new_bar.high}")
    print(f"  最低价: {new_bar.low}")
    print(f"  成交量: {new_bar.vol}")
    print(f"  成交额: {new_bar.amount}")
    
    # 测试elements属性
    elements = new_bar.elements
    print(f"  包含的RawBar数量: {len(elements)}")
    
    for i, raw_bar in enumerate(elements):
        print(f"    RawBar {i+1}: 开盘={raw_bar.open}, 收盘={raw_bar.close}")
    
    # 测试raw_bars方法
    raw_bars = new_bar.raw_bars()
    print(f"  raw_bars()方法返回数量: {len(raw_bars)}")
    
    print("✓ NewBar属性测试通过")

def test_fx_with_newbar():
    """测试FX对象中的NewBar功能"""
    print("\n🔺 测试FX中的NewBar功能...")
    
    # 创建测试数据
    bars = []
    base_dt = datetime(2025, 1, 1)
    
    # 创建一个波动的价格序列来产生分型
    prices = [100, 102, 98, 104, 96, 108, 94, 110, 92, 112]
    
    for i, price in enumerate(prices):
        dt = base_dt + timedelta(days=i)
        unix_timestamp = int(dt.timestamp())
        
        bar = RawBar(
            symbol="TEST.SZ",
            dt_utc_timestamp=unix_timestamp,
            freq=Freq.D,
            open=price - 0.5,
            close=price,
            high=price + 1.0,
            low=price - 1.0,
            vol=1000000.0,
            amount=price * 1000000.0
        )
        bars.append(bar)
    
    # 创建CZSC对象
    czsc = CZSC(bars, max_bi_num=50)
    
    # 获取分型列表
    fx_list = czsc.get_fx_list()
    print(f"  识别到{len(fx_list)}个分型")
    
    for i, fx in enumerate(fx_list):
        print(f"  分型 {i+1}: {fx.mark}, 值={fx.fx:.2f}")
        
        # 测试FX的new_bars属性（NewBar列表）
        new_bars = fx.new_bars
        print(f"    包含{len(new_bars)}个NewBar")

        for j, new_bar in enumerate(new_bars):
            print(f"      NewBar {j+1}: 开盘={new_bar.open:.2f}, 收盘={new_bar.close:.2f}")
        
        # 测试FX的raw_bars属性
        raw_bars = fx.raw_bars
        print(f"    包含{len(raw_bars)}个RawBar")
        
        # 测试FX的其他新属性
        print(f"    分型强度: {fx.power_str}")
        print(f"    成交量力度: {fx.power_volume:.0f}")
        print(f"    是否有重叠中枢: {fx.has_zs}")
    
    print("✓ FX中的NewBar功能测试通过")

def test_bi_with_newbar():
    """测试BI对象中的NewBar功能"""
    print("\n📈 测试BI中的NewBar功能...")
    
    # 使用之前的测试数据
    bars = []
    base_dt = datetime(2025, 1, 1)
    
    # 创建更多数据以产生笔
    prices = [100, 102, 104, 106, 108, 106, 104, 102, 100, 98, 96, 98, 100, 102, 104]
    
    for i, price in enumerate(prices):
        dt = base_dt + timedelta(days=i)
        unix_timestamp = int(dt.timestamp())
        
        bar = RawBar(
            symbol="TEST.SZ",
            dt_utc_timestamp=unix_timestamp,
            freq=Freq.D,
            open=price - 0.5,
            close=price,
            high=price + 1.0,
            low=price - 1.0,
            vol=1000000.0,
            amount=price * 1000000.0
        )
        bars.append(bar)
    
    # 创建CZSC对象
    czsc = CZSC(bars, max_bi_num=50)
    
    # 获取笔列表
    bi_list = czsc.bi_list
    print(f"  识别到{len(bi_list)}笔")
    
    for i, bi in enumerate(bi_list):
        print(f"  笔 {i+1}: {bi.direction}, 高点={bi.high:.2f}, 低点={bi.low:.2f}")
        
        # 测试笔的分型
        fx_a = bi.fx_a
        fx_b = bi.fx_b
        print(f"    起始分型: {fx_a.mark}, 值={fx_a.fx:.2f}")
        print(f"    结束分型: {fx_b.mark}, 值={fx_b.fx:.2f}")
        
        # 测试起始分型的NewBar
        fx_a_new_bars = fx_a.new_bars
        print(f"    起始分型包含{len(fx_a_new_bars)}个NewBar")
        
        # 测试笔内分型
        fxs = bi.fxs
        print(f"    笔内包含{len(fxs)}个分型")
    
    print("✓ BI中的NewBar功能测试通过")

def main():
    """主函数"""
    print("🚀 NewBar Python绑定功能测试")
    print("=" * 50)
    
    # 1. 测试NewBar创建
    new_bar = test_newbar_creation()
    
    # 2. 测试NewBar属性
    test_newbar_properties(new_bar)
    
    # 3. 测试FX中的NewBar功能
    test_fx_with_newbar()
    
    # 4. 测试BI中的NewBar功能
    test_bi_with_newbar()
    
    print(f"\n✅ 所有NewBar功能测试通过!")
    print(f"💡 NewBar已成功暴露给Python，可以完整访问CZSC的所有功能")

if __name__ == "__main__":
    try:
        main()
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保已正确安装rs_czsc模块")
        print("   运行: maturin develop")
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()
