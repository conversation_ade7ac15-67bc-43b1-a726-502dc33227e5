mod core;
mod errors;
mod trader;
mod utils;

use core::objects::{print_it, PyBarGenerator, PyBI, PyCZSC, PyDirection, PyFreq, PyFX, PyMarket, PyMark, PyRawBar};

use pyo3::prelude::*;
use trader::weight_backtest::PyWeightBacktest;
use utils::corr::normalize_feature;
use utils::daily_performance::daily_performance;
use utils::top_drawdowns::top_drawdowns;

#[pymodule]
fn _rs_czsc(_py: Python, m: &Bound<PyModule>) -> PyResult<()> {
    m.add_function(wrap_pyfunction!(daily_performance, m)?)?;
    m.add_function(wrap_pyfunction!(top_drawdowns, m)?)?;
    m.add_function(wrap_pyfunction!(normalize_feature, m)?)?;
    m.add_class::<PyWeightBacktest>()?;

    m.add_function(wrap_pyfunction!(print_it, m)?)?;
    m.add_class::<PyFreq>()?;
    m.add_class::<PyMarket>()?;
    m.add_class::<PyRawBar>()?;
    m.add_class::<PyBarGenerator>()?;

    // CZSC相关类型
    m.add_class::<PyCZSC>()?;
    m.add_class::<PyBI>()?;
    m.add_class::<PyFX>()?;
    m.add_class::<PyDirection>()?;
    m.add_class::<PyMark>()?;

    Ok(())
}
