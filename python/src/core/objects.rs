use anyhow::anyhow;
use chrono::DateTime;
use czsc::{
    core::{
        analyze::CZSC,
        objects::{
            bar::{NewBar, NewBarBuilder, RawBar, RawBarBuilder},
            bi::BI,
            direction::Direction,
            freq::Freq,
            fx::FX,
            market::Market,
            mark::Mark,
        },
    },
    utils::bar_generator::BarGenerator,
};
use pyo3::prelude::*;
use crate::errors::PythonError;

#[pyclass(eq, eq_int, name = "Market")]
#[derive(PartialEq, Clone)]
pub enum PyMarket {
    /// A股
    AShare,
    /// 期货
    Futures,
    /// 默认
    Default,
}

impl From<PyMarket> for Market {
    fn from(value: PyMarket) -> Self {
        match value {
            PyMarket::AShare => Market::AShare,
            PyMarket::Futures => Market::Futures,
            PyMarket::Default => Market::Default,
        }
    }
}

#[pyclass(eq, eq_int, name = "Freq")]
#[derive(PartialEq, Clone)]
pub enum PyFreq {
    /// 逐笔
    Tick,
    /// 1分钟
    F1,
    /// 2分钟
    F2,
    /// 3分钟
    F3,
    /// 4分钟
    F4,
    /// 5分钟
    F5,
    /// 6分钟
    F6,
    /// 10分钟
    F10,
    /// 12分钟
    F12,
    /// 15分钟
    F15,
    /// 20分钟
    F20,
    /// 30分钟
    F30,
    /// 60分钟
    F60,
    /// 120分钟
    F120,
    /// 240分钟
    F240,
    /// 360分钟
    F360,
    /// 日线
    D,
    /// 周线
    W,
    /// 月线
    M,
    /// 季线
    S,
    /// 年线
    Y,
}

impl From<PyFreq> for Freq {
    fn from(value: PyFreq) -> Self {
        match value {
            PyFreq::Tick => Freq::Tick,
            PyFreq::F1 => Freq::F1,
            PyFreq::F2 => Freq::F2,
            PyFreq::F3 => Freq::F3,
            PyFreq::F4 => Freq::F4,
            PyFreq::F5 => Freq::F5,
            PyFreq::F6 => Freq::F6,
            PyFreq::F10 => Freq::F10,
            PyFreq::F12 => Freq::F12,
            PyFreq::F15 => Freq::F15,
            PyFreq::F20 => Freq::F20,
            PyFreq::F30 => Freq::F30,
            PyFreq::F60 => Freq::F60,
            PyFreq::F120 => Freq::F120,
            PyFreq::F240 => Freq::F240,
            PyFreq::F360 => Freq::F360,
            PyFreq::D => Freq::D,
            PyFreq::W => Freq::W,
            PyFreq::M => Freq::M,
            PyFreq::S => Freq::S,
            PyFreq::Y => Freq::Y,
        }
    }
}

#[pyclass(eq, eq_int, name = "Direction")]
#[derive(PartialEq, Clone)]
pub enum PyDirection {
    /// 向上
    Up,
    /// 向下
    Down,
}

impl From<Direction> for PyDirection {
    fn from(value: Direction) -> Self {
        match value {
            Direction::Up => PyDirection::Up,
            Direction::Down => PyDirection::Down,
        }
    }
}

impl From<PyDirection> for Direction {
    fn from(value: PyDirection) -> Self {
        match value {
            PyDirection::Up => Direction::Up,
            PyDirection::Down => Direction::Down,
        }
    }
}

#[pyclass(eq, eq_int, name = "Mark")]
#[derive(PartialEq, Clone)]
pub enum PyMark {
    /// 顶分型
    G,
    /// 底分型
    D,
}

impl From<Mark> for PyMark {
    fn from(value: Mark) -> Self {
        match value {
            Mark::G => PyMark::G,
            Mark::D => PyMark::D,
        }
    }
}

impl From<PyMark> for Mark {
    fn from(value: PyMark) -> Self {
        match value {
            PyMark::G => Mark::G,
            PyMark::D => Mark::D,
        }
    }
}

#[pyclass(name = "BarGenerator")]
#[repr(transparent)]
pub struct PyBarGenerator {
    inner: BarGenerator,
}

#[pymethods]
impl PyBarGenerator {
    #[new]
    fn new(
        base_freq: PyFreq,
        freqs: Vec<PyFreq>,
        max_count: usize,
        market: PyMarket,
    ) -> PyResult<Self> {
        Ok(PyBarGenerator {
            inner: BarGenerator::new(
                base_freq.into(),
                freqs.into_iter().map(|f| f.into()).collect(),
                max_count,
                market.into(),
            )
            .map_err(|e| PythonError::Utils(e))?,
        })
    }

    /// 初始化某个周期的K线序列
    ///
    /// # 函数计算逻辑
    ///
    /// 1. 检查输入的`freq`是否存在于`self.freq_bars`的键中。如果不存在，返回错误。
    /// 2. 检查`self.freq_bars[freq]`是否为空。如果不为空，返回错误，表示不允许重复初始化。
    /// 3. 如果以上检查都通过，将输入的`bars`存储到`self.freq_bars[freq]`中。
    /// 4. 从`bars`中获取最后一根K线的交易标的代码，更新`self.symbol`。
    ///
    /// # Arguments
    ///
    /// * `freq` - 周期名称
    /// * `bars` - K线序列
    fn init_freq_bars(&mut self, freq: PyFreq, bars: Vec<PyRawBar>) -> PyResult<()> {
        self.inner
            .init_freq_bars(freq.into(), bars.into_iter().map(|b| b.inner))
            .map_err(|e| PythonError::Utils(e))?;
        Ok(())
    }

    /// 获取最新K线日期
    fn get_latest_date(&self) -> Option<String> {
        self.inner
            .get_latest_date()
            .and_then(|dt| Some(dt.to_string()))
    }

    /// 获取所属品种
    fn get_symbol(&self) -> Option<String> {
        self.inner.get_symbol().and_then(|s| Some(s.to_string()))
    }

    /// 更新各周期K线
    ///
    /// # 函数计算逻辑
    ///
    /// 1. 获取基准周期`base_freq`，并验证输入`bar`的周期值是否与之匹配
    /// 2. 更新`self.symbol`和`self.end_dt`为当前K线的对应值
    /// 3. 检查重复性：
    ///    - 检查`self.bars[base_freq]`中是否已存在相同时间的K线
    ///    - 如果存在重复K线，返回错误，不进行更新
    /// 4. 如果无重复，遍历所有周期：
    ///    - 对每个周期调用`update_freq`方法更新K线数据
    /// 5. 维护数据量：
    ///    - 遍历所有周期的K线数据
    ///    - 确保每个周期的K线数量不超过`max_count`
    ///    - 如果超过限制，保留最新的`max_count`条数据
    ///
    /// # Arguments
    ///
    /// * `bar` - 已完成的基准周期K线的引用
    fn update(&self, bar: PyRawBar) -> PyResult<()> {
        self.inner
            .update(&bar.inner)
            .map_err(|e| PythonError::Utils(e))?;
        Ok(())
    }
}

#[pyclass(name = "FX")]
#[derive(Clone)]
pub struct PyFX {
    inner: FX,
}

#[pymethods]
impl PyFX {
    #[getter]
    fn symbol(&self) -> String {
        self.inner.symbol.to_string()
    }

    #[getter]
    fn dt(&self) -> i64 {
        self.inner.dt.timestamp()
    }

    #[getter]
    fn mark(&self) -> PyMark {
        self.inner.mark.clone().into()
    }

    #[getter]
    fn high(&self) -> f64 {
        self.inner.high
    }

    #[getter]
    fn low(&self) -> f64 {
        self.inner.low
    }

    #[getter]
    fn fx(&self) -> f64 {
        self.inner.fx
    }

    /// 获取构成分型的NewBar列表
    #[getter]
    fn new_bars(&self) -> Vec<PyNewBar> {
        self.inner
            .elements
            .iter()
            .cloned()
            .map(|new_bar| PyNewBar { inner: new_bar })
            .collect()
    }

    /// 获取原始K线列表（从NewBar的elements中提取）
    #[getter]
    fn raw_bars(&self) -> Vec<PyRawBar> {
        self.inner
            .elements
            .iter()
            .flat_map(|new_bar| new_bar.elements.clone())
            .map(|raw_bar| PyRawBar { inner: raw_bar })
            .collect()
    }

    /// 获取分型强度字符串
    #[getter]
    fn power_str(&self) -> String {
        self.inner.power_str().to_string()
    }

    /// 获取成交量力度
    #[getter]
    fn power_volume(&self) -> f64 {
        self.inner.power_volume()
    }

    /// 判断是否有重叠中枢
    #[getter]
    fn has_zs(&self) -> bool {
        self.inner.has_zs()
    }




    fn __repr__(&self) -> String {
        format!(
            "FX(symbol={}, dt={}, mark={:?}, fx={})",
            self.inner.symbol,
            self.inner.dt.format("%Y-%m-%d %H:%M:%S"),
            self.inner.mark,
            self.inner.fx
        )
    }
}

impl From<FX> for PyFX {
    fn from(fx: FX) -> Self {
        PyFX { inner: fx }
    }
}

#[pyclass(name = "BI")]
#[derive(Clone)]
pub struct PyBI {
    inner: BI,
}

#[pymethods]
impl PyBI {
    #[getter]
    fn symbol(&self) -> String {
        self.inner.symbol.to_string()
    }

    #[getter]
    fn direction(&self) -> PyDirection {
        self.inner.direction.into()
    }

    #[getter]
    fn high(&self) -> f64 {
        self.inner.high()
    }

    #[getter]
    fn low(&self) -> f64 {
        self.inner.low()
    }

    #[getter]
    fn sdt(&self) -> i64 {
        self.inner.sdt().timestamp()
    }

    #[getter]
    fn edt(&self) -> i64 {
        self.inner.edt().timestamp()
    }

    #[getter]
    fn fx_a(&self) -> PyFX {
        self.inner.fx_a.clone().into()
    }

    #[getter]
    fn fx_b(&self) -> PyFX {
        self.inner.fx_b.clone().into()
    }

    #[getter]
    fn fxs(&self) -> Vec<PyFX> {
        self.inner.fxs.iter().cloned().map(|fx| fx.into()).collect()
    }

    fn __repr__(&self) -> String {
        format!(
            "BI(symbol={}, sdt={}, edt={}, direction={:?}, high={}, low={})",
            self.inner.symbol,
            self.inner.sdt().format("%Y-%m-%d %H:%M:%S"),
            self.inner.edt().format("%Y-%m-%d %H:%M:%S"),
            self.inner.direction,
            self.inner.high(),
            self.inner.low()
        )
    }
}

impl From<BI> for PyBI {
    fn from(bi: BI) -> Self {
        PyBI { inner: bi }
    }
}

#[pyclass(name = "NewBar")]
#[derive(Clone)]
pub struct PyNewBar {
    inner: NewBar,
}

#[pymethods]
impl PyNewBar {
    #[new]
    #[pyo3(signature = (symbol, dt_utc_timestamp, freq, id, open, close, high, low, vol, amount, elements=None))]
    fn new(
        symbol: &str,
        dt_utc_timestamp: i64,
        freq: PyFreq,
        id: i32,
        open: f64,
        close: f64,
        high: f64,
        low: f64,
        vol: f64,
        amount: f64,
        elements: Option<Vec<PyRawBar>>,
    ) -> PyResult<Self> {
        let dt = DateTime::from_timestamp(dt_utc_timestamp, 0).ok_or(PythonError::Unexpected(
            anyhow!("Invalid timestamp for building NewBar"),
        ))?;

        let raw_elements: Vec<RawBar> = elements
            .unwrap_or_default()
            .into_iter()
            .map(|py_bar| py_bar.inner)
            .collect();

        Ok(PyNewBar {
            inner: NewBarBuilder::default()
                .symbol(symbol)
                .dt(dt)
                .freq(Freq::from(freq))
                .id(id)
                .open(open)
                .close(close)
                .high(high)
                .low(low)
                .vol(vol)
                .amount(amount)
                .elements(raw_elements)
                .build()
                .map_err(|e| PythonError::Unexpected(anyhow!("{:?}", e)))?,
        })
    }

    #[getter]
    fn symbol(&self) -> String {
        self.inner.symbol.to_string()
    }

    #[getter]
    fn dt(&self) -> i64 {
        self.inner.dt.timestamp()
    }

    #[getter]
    fn freq(&self) -> PyFreq {
        match self.inner.freq {
            Freq::Tick => PyFreq::Tick,
            Freq::F1 => PyFreq::F1,
            Freq::F2 => PyFreq::F2,
            Freq::F3 => PyFreq::F3,
            Freq::F4 => PyFreq::F4,
            Freq::F5 => PyFreq::F5,
            Freq::F6 => PyFreq::F6,
            Freq::F10 => PyFreq::F10,
            Freq::F12 => PyFreq::F12,
            Freq::F15 => PyFreq::F15,
            Freq::F20 => PyFreq::F20,
            Freq::F30 => PyFreq::F30,
            Freq::F60 => PyFreq::F60,
            Freq::F120 => PyFreq::F120,
            Freq::F240 => PyFreq::F240,
            Freq::F360 => PyFreq::F360,
            Freq::D => PyFreq::D,
            Freq::W => PyFreq::W,
            Freq::M => PyFreq::M,
            Freq::S => PyFreq::S,
            Freq::Y => PyFreq::Y,
        }
    }

    #[getter]
    fn id(&self) -> i32 {
        self.inner.id
    }

    #[getter]
    fn open(&self) -> f64 {
        self.inner.open
    }

    #[getter]
    fn close(&self) -> f64 {
        self.inner.close
    }

    #[getter]
    fn high(&self) -> f64 {
        self.inner.high
    }

    #[getter]
    fn low(&self) -> f64 {
        self.inner.low
    }

    #[getter]
    fn vol(&self) -> f64 {
        self.inner.vol
    }

    #[getter]
    fn amount(&self) -> f64 {
        self.inner.amount
    }

    /// 获取原始K线列表
    #[getter]
    fn elements(&self) -> Vec<PyRawBar> {
        self.inner
            .elements
            .iter()
            .cloned()
            .map(|raw_bar| PyRawBar { inner: raw_bar })
            .collect()
    }

    /// 获取原始K线列表（别名方法）
    fn raw_bars(&self) -> Vec<PyRawBar> {
        self.elements()
    }

    fn __repr__(&self) -> String {
        format!(
            "NewBar(symbol={}, dt={}, freq={:?}, id={}, open={}, close={}, high={}, low={}, vol={}, amount={}, elements_count={})",
            self.inner.symbol,
            self.inner.dt.format("%Y-%m-%d %H:%M:%S"),
            self.inner.freq,
            self.inner.id,
            self.inner.open,
            self.inner.close,
            self.inner.high,
            self.inner.low,
            self.inner.vol,
            self.inner.amount,
            self.inner.elements.len()
        )
    }
}

impl From<NewBar> for PyNewBar {
    fn from(new_bar: NewBar) -> Self {
        PyNewBar { inner: new_bar }
    }
}

#[pyclass(name = "RawBar")]
#[repr(transparent)]
#[derive(Clone)]
pub struct PyRawBar {
    inner: RawBar,
}

#[pymethods]
impl PyRawBar {
    #[new]
    fn new(
        symbol: &str,
        dt_utc_timestamp: i64,
        freq: PyFreq,
        open: f64,
        close: f64,
        high: f64,
        id:
        low: f64,
        vol: f64,
        amount: f64,
    ) -> PyResult<Self> {
        let dt = DateTime::from_timestamp(dt_utc_timestamp, 0).ok_or(PythonError::Unexpected(
            anyhow!("Invalid timestamp for building Rawbar"),
        ))?;
        Ok(PyRawBar {
            inner: RawBarBuilder::default()
                .symbol(symbol)
                .dt(dt)
                .freq(Freq::from(freq))
                .open(open)
                .close(close)
                .high(high)
                .low(low)
                .vol(vol)
                .id(0)
                .amount(amount)
                .build()
                .map_err(|e| PythonError::Unexpected(anyhow!("{:?}", e)))?,
        })
    }

    #[getter]
    fn symbol(&self) -> String {
        self.inner.symbol.to_string()
    }

    #[getter]
    fn dt(&self) -> i64 {
        self.inner.dt.timestamp()
    }

    #[getter]
    fn freq(&self) -> PyFreq {
        match self.inner.freq {
            Freq::Tick => PyFreq::Tick,
            Freq::F1 => PyFreq::F1,
            Freq::F2 => PyFreq::F2,
            Freq::F3 => PyFreq::F3,
            Freq::F4 => PyFreq::F4,
            Freq::F5 => PyFreq::F5,
            Freq::F6 => PyFreq::F6,
            Freq::F10 => PyFreq::F10,
            Freq::F12 => PyFreq::F12,
            Freq::F15 => PyFreq::F15,
            Freq::F20 => PyFreq::F20,
            Freq::F30 => PyFreq::F30,
            Freq::F60 => PyFreq::F60,
            Freq::F120 => PyFreq::F120,
            Freq::F240 => PyFreq::F240,
            Freq::F360 => PyFreq::F360,
            Freq::D => PyFreq::D,
            Freq::W => PyFreq::W,
            Freq::M => PyFreq::M,
            Freq::S => PyFreq::S,
            Freq::Y => PyFreq::Y,
        }
    }

    #[getter]
    fn id(&self) -> i32 {
        self.inner.id
    }

    #[getter]
    fn open(&self) -> f64 {
        self.inner.open
    }

    #[getter]
    fn close(&self) -> f64 {
        self.inner.close
    }

    #[getter]
    fn high(&self) -> f64 {
        self.inner.high
    }

    #[getter]
    fn low(&self) -> f64 {
        self.inner.low
    }

    #[getter]
    fn vol(&self) -> f64 {
        self.inner.vol
    }

    #[getter]
    fn amount(&self) -> f64 {
        self.inner.amount
    }

    /// 上影线长度
    #[getter]
    fn upper(&self) -> f64 {
        self.inner.upper()
    }

    /// 下影线长度
    #[getter]
    fn lower(&self) -> f64 {
        self.inner.lower()
    }

    /// 实体长度
    #[getter]
    fn solid(&self) -> f64 {
        self.inner.solid()
    }

    fn __repr__(&self) -> String {
        format!(
            "RawBar(symbol={}, dt={}, freq={:?}, id={}, open={}, close={}, high={}, low={}, vol={}, amount={})",
            self.inner.symbol,
            self.inner.dt.format("%Y-%m-%d %H:%M:%S"),
            self.inner.freq,
            self.inner.id,
            self.inner.open,
            self.inner.close,
            self.inner.high,
            self.inner.low,
            self.inner.vol,
            self.inner.amount
        )
    }
}

#[pyclass(name = "CZSC")]
pub struct PyCZSC {
    inner: CZSC,
}

#[pymethods]
impl PyCZSC {
    #[new]
    fn new(bars_raw: Vec<PyRawBar>, max_bi_num: usize) -> PyResult<Self> {
        let rust_bars: Vec<RawBar> = bars_raw.into_iter().map(|b| b.inner).collect();
        Ok(PyCZSC {
            inner: CZSC::new(rust_bars, max_bi_num),
        })
    }

    #[getter]
    fn symbol(&self) -> String {
        self.inner.symbol.to_string()
    }

    #[getter]
    fn freq(&self) -> PyFreq {
        match self.inner.freq {
            Freq::Tick => PyFreq::Tick,
            Freq::F1 => PyFreq::F1,
            Freq::F2 => PyFreq::F2,
            Freq::F3 => PyFreq::F3,
            Freq::F4 => PyFreq::F4,
            Freq::F5 => PyFreq::F5,
            Freq::F6 => PyFreq::F6,
            Freq::F10 => PyFreq::F10,
            Freq::F12 => PyFreq::F12,
            Freq::F15 => PyFreq::F15,
            Freq::F20 => PyFreq::F20,
            Freq::F30 => PyFreq::F30,
            Freq::F60 => PyFreq::F60,
            Freq::F120 => PyFreq::F120,
            Freq::F240 => PyFreq::F240,
            Freq::F360 => PyFreq::F360,
            Freq::D => PyFreq::D,
            Freq::W => PyFreq::W,
            Freq::M => PyFreq::M,
            Freq::S => PyFreq::S,
            Freq::Y => PyFreq::Y,
        }
    }

    #[getter]
    fn max_bi_num(&self) -> usize {
        self.inner.max_bi_num
    }

    #[getter]
    fn bi_list(&self) -> Vec<PyBI> {
        self.inner.bi_list.iter().cloned().map(|bi| bi.into()).collect()
    }

    /// 获取分型列表
    fn get_fx_list(&self) -> Vec<PyFX> {
        self.inner.get_fx_list().into_iter().map(|fx| fx.into()).collect()
    }

    /// 更新K线数据
    fn update(&mut self, bar: PyRawBar) -> PyResult<()> {
        self.inner.update(bar.inner);
        Ok(())
    }

    fn __repr__(&self) -> String {
        format!(
            "CZSC(symbol={}, freq={:?}, max_bi_num={}, bi_count={})",
            self.inner.symbol,
            self.inner.freq,
            self.inner.max_bi_num,
            self.inner.bi_list.len()
        )
    }
}

#[pyfunction]
pub fn print_it(dt_utc_timestamp: i64) -> PyResult<()> {
    let dt = DateTime::from_timestamp(dt_utc_timestamp, 0).ok_or(PythonError::Unexpected(
        anyhow!("Invalid timestamp for building Rawbar"),
    ))?;
    let dt_naive = dt.naive_utc();
    println!("{}", dt_naive);
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_print_it() {
        // print_it(1734003461);
        let res = print_it(-99999999999999999);
        println!("{:?}", res.err());
    }
}
