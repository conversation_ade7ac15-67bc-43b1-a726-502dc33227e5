#!/usr/bin/env python3
"""
RS-CZSC Python绑定完整示例

这个示例展示了如何使用RS-CZSC Python绑定进行缠论分析。
包括：
1. 创建K线数据
2. CZSC分析
3. 笔和分型分析
4. 动态更新
5. 结果可视化（可选）
"""

import sys
import os
from datetime import datetime, timedelta
from typing import List

# 添加父目录到路径，以便导入rs_czsc
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rs_czsc._rs_czsc import RawBar, Freq, CZSC, Direction, Mark


def create_realistic_bars(symbol: str = "000001.SZ", count: int = 100) -> List[RawBar]:
    """
    创建更真实的K线数据，模拟股票价格走势
    
    Args:
        symbol: 股票代码
        count: K线数量
        
    Returns:
        K线数据列表
    """
    bars = []
    base_dt = datetime(2024, 1, 1)
    base_price = 100.0
    
    # 模拟价格走势：多个阶段的涨跌
    phases = [
        (20, 1.5),   # 前20根：上涨
        (15, -2.0),  # 接下来15根：下跌
        (25, 0.8),   # 接下来25根：震荡上涨
        (20, -1.2),  # 接下来20根：下跌
        (20, 2.0),   # 最后20根：强势上涨
    ]
    
    current_price = base_price
    bar_index = 0
    
    for phase_length, trend in phases:
        if bar_index >= count:
            break
            
        for i in range(min(phase_length, count - bar_index)):
            dt = base_dt + timedelta(days=bar_index)
            unix_timestamp = int(dt.timestamp())
            
            # 计算价格变化
            daily_change = trend + (i % 5 - 2) * 0.5  # 添加随机波动
            current_price += daily_change
            
            # 确保价格为正
            current_price = max(current_price, 10.0)
            
            # 生成OHLC数据
            open_price = current_price - daily_change
            close_price = current_price
            high_price = max(open_price, close_price) + abs(daily_change) * 0.3
            low_price = min(open_price, close_price) - abs(daily_change) * 0.3
            
            # 生成成交量（模拟）
            volume = 1000000 + (bar_index % 10) * 100000
            amount = close_price * volume
            
            bar = RawBar(
                symbol=symbol,
                dt_utc_timestamp=unix_timestamp,
                freq=Freq.D,
                open=open_price,
                close=close_price,
                high=high_price,
                low=low_price,
                vol=volume,
                amount=amount
            )
            bars.append(bar)
            bar_index += 1
    
    return bars


def analyze_czsc(bars: List[RawBar], max_bi_num: int = 50) -> CZSC:
    """
    执行CZSC分析
    
    Args:
        bars: K线数据
        max_bi_num: 最大笔数量
        
    Returns:
        CZSC分析对象
    """
    print("🔍 开始CZSC分析...")
    czsc = CZSC(bars, max_bi_num)
    
    print(f"📊 基本信息:")
    print(f"   交易标的: {czsc.symbol}")
    print(f"   K线频率: {czsc.freq}")
    print(f"   最大笔数: {czsc.max_bi_num}")
    print(f"   K线数量: {len(bars)}")
    
    return czsc


def analyze_bi_list(czsc: CZSC):
    """分析笔列表"""
    bi_list = czsc.bi_list
    print(f"\n📈 笔分析 (共{len(bi_list)}笔):")
    
    if not bi_list:
        print("   暂无笔数据")
        return
    
    for i, bi in enumerate(bi_list):
        direction_str = "📈 向上" if bi.direction == Direction.Up else "📉 向下"
        duration = bi.edt - bi.sdt
        price_range = bi.high - bi.low
        
        print(f"   笔{i+1:2d}: {direction_str} | "
              f"高点={bi.high:7.2f} | 低点={bi.low:7.2f} | "
              f"幅度={price_range:6.2f} | 时长={duration//86400}天")
    
    # 统计信息
    up_bis = [bi for bi in bi_list if bi.direction == Direction.Up]
    down_bis = [bi for bi in bi_list if bi.direction == Direction.Down]
    
    print(f"\n📊 笔统计:")
    print(f"   向上笔: {len(up_bis)}个")
    print(f"   向下笔: {len(down_bis)}个")
    
    if up_bis:
        avg_up_range = sum(bi.high - bi.low for bi in up_bis) / len(up_bis)
        print(f"   平均上涨幅度: {avg_up_range:.2f}")
    
    if down_bis:
        avg_down_range = sum(bi.high - bi.low for bi in down_bis) / len(down_bis)
        print(f"   平均下跌幅度: {avg_down_range:.2f}")


def analyze_fx_list(czsc: CZSC):
    """分析分型列表"""
    fx_list = czsc.get_fx_list()
    print(f"\n🔺 分型分析 (共{len(fx_list)}个):")
    
    if not fx_list:
        print("   暂无分型数据")
        return
    
    for i, fx in enumerate(fx_list):
        mark_str = "🔺 顶分型" if fx.mark == Mark.G else "🔻 底分型"
        dt_str = datetime.fromtimestamp(fx.dt).strftime("%Y-%m-%d")
        
        print(f"   分型{i+1:2d}: {mark_str} | "
              f"值={fx.fx:7.2f} | 时间={dt_str}")
    
    # 统计信息
    top_fxs = [fx for fx in fx_list if fx.mark == Mark.G]
    bottom_fxs = [fx for fx in fx_list if fx.mark == Mark.D]
    
    print(f"\n📊 分型统计:")
    print(f"   顶分型: {len(top_fxs)}个")
    print(f"   底分型: {len(bottom_fxs)}个")
    
    if len(fx_list) >= 2:
        price_range = max(fx.fx for fx in fx_list) - min(fx.fx for fx in fx_list)
        print(f"   价格区间: {price_range:.2f}")


def demo_dynamic_update(initial_bars: List[RawBar]):
    """演示动态更新功能"""
    print(f"\n🔄 动态更新演示:")
    
    # 使用前80%的数据作为初始数据
    split_point = int(len(initial_bars) * 0.8)
    init_bars = initial_bars[:split_point]
    new_bars = initial_bars[split_point:]
    
    czsc = CZSC(init_bars, max_bi_num=50)
    print(f"   初始状态: {len(czsc.bi_list)}笔, {len(czsc.get_fx_list())}分型")
    
    # 逐个添加新K线
    for i, bar in enumerate(new_bars):
        czsc.update(bar)
        bi_count = len(czsc.bi_list)
        fx_count = len(czsc.get_fx_list())
        print(f"   添加K线{i+1:2d}: {bi_count}笔, {fx_count}分型")


def main():
    """主函数"""
    print("🚀 RS-CZSC Python绑定完整示例")
    print("=" * 50)
    
    # 1. 创建测试数据
    print("\n📊 创建测试数据...")
    bars = create_realistic_bars("000001.SZ", 100)
    print(f"   创建了{len(bars)}根日K线数据")
    
    # 2. CZSC分析
    czsc = analyze_czsc(bars)
    
    # 3. 笔分析
    analyze_bi_list(czsc)
    
    # 4. 分型分析
    analyze_fx_list(czsc)
    
    # 5. 动态更新演示
    demo_dynamic_update(bars)
    
    print(f"\n✅ 示例运行完成!")
    print(f"💡 提示: 可以修改参数来测试不同的场景")


if __name__ == "__main__":
    try:
        main()
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保已正确安装rs_czsc模块")
        print("   运行: maturin develop")
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()
