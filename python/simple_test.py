"""
简化的CZSC测试
"""
from datetime import datetime, timedelta

# 直接从底层模块导入
from rs_czsc._rs_czsc import RawBar, Freq, CZSC, Direction, Mark

def create_simple_bars(count=20):
    """创建简单的测试K线数据"""
    bars = []
    base_dt = datetime(2025, 1, 1)
    
    for i in range(count):
        dt = base_dt + timedelta(days=i)
        unix_timestamp = int(dt.timestamp())
        
        # 简单的价格序列：先涨后跌
        if i < 10:
            price = 100 + i * 2  # 上涨
        else:
            price = 120 - (i - 10) * 1.5  # 下跌
        
        bar = RawBar(
            symbol="TEST.SZ",
            dt_utc_timestamp=unix_timestamp,
            freq=Freq.D,
            open=price - 0.5,
            close=price,
            high=price + 1.0,
            low=price - 1.0,
            vol=1000000.0,
            amount=price * 1000000.0
        )
        bars.append(bar)
    
    return bars

def test_basic_functionality():
    """测试基本功能"""
    print("开始测试CZSC基本功能...")
    
    # 创建测试数据
    bars = create_simple_bars(20)
    print(f"创建了 {len(bars)} 根K线")
    
    # 创建CZSC对象
    czsc = CZSC(bars, max_bi_num=50)
    print(f"CZSC对象创建成功: {czsc}")
    
    # 测试基本属性
    print(f"符号: {czsc.symbol}")
    print(f"频率: {czsc.freq}")
    print(f"最大笔数: {czsc.max_bi_num}")
    
    # 测试笔列表
    bi_list = czsc.bi_list
    print(f"笔的数量: {len(bi_list)}")
    
    for i, bi in enumerate(bi_list):
        print(f"笔 {i+1}: 方向={bi.direction}, 高点={bi.high:.2f}, 低点={bi.low:.2f}")
    
    # 测试分型列表
    fx_list = czsc.get_fx_list()
    print(f"分型数量: {len(fx_list)}")
    
    for i, fx in enumerate(fx_list):
        print(f"分型 {i+1}: 标记={fx.mark}, 值={fx.fx:.2f}")
    
    print("✓ 所有测试通过！")

if __name__ == "__main__":
    test_basic_functionality()
